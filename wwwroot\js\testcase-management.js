﻿$(document).ready(function () {
    let selectedTestCases = new Set();

    // Initialize
    updateSelectedCount();
    setupEventHandlers();

    function setupEventHandlers() {
        // Filter inputs
        $('#filterTestCaseName, #filterTestCaseCode, #filterSuiteName, #filterModuleName, #filterLabels')
            .on('input', debounce(applyFilters, 300));

        // Clear filters
        $('#clearFilters').on('click', clearAllFilters);

        // Selection controls
        $('#selectAll').on('click', selectAllTestCases);
        $('#selectNone').on('click', selectNoTestCases);
        $('#selectVisible').on('click', selectVisibleTestCases);
        $('#selectAllCheckbox').on('change', toggleAllCheckboxes);

        // Individual checkboxes
        $(document).on('change', '.test-case-checkbox', handleCheckboxChange);

        // Run tests
        $('#runSelectedTests').on('click', showRunTestsModal);
        $('#confirmRunTests').on('click', runSelectedTests);

        // View test runs
        $('#viewTestRuns').on('click', showTestRunsModal);
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function applyFilters() {
        const filters = {
            testCaseName: $('#filterTestCaseName').val().toLowerCase(),
            testCaseCode: $('#filterTestCaseCode').val().toLowerCase(),
            suiteName: $('#filterSuiteName').val().toLowerCase(),
            moduleName: $('#filterModuleName').val().toLowerCase(),
            labels: $('#filterLabels').val().toLowerCase()
        };

        let visibleCount = 0;

        $('#testCasesTable tbody tr').each(function () {
            const row = $(this);
            const testCaseName = row.find('.test-case-name').text().toLowerCase();
            const testCaseCode = row.find('.test-case-code').text().toLowerCase();
            const suiteName = row.find('.suite-name').text().toLowerCase();
            const moduleName = row.find('.module-name').text().toLowerCase();
            const labels = row.find('.labels').text().toLowerCase();

            const matches = (
                (filters.testCaseName === '' || testCaseName.includes(filters.testCaseName)) &&
                (filters.testCaseCode === '' || testCaseCode.includes(filters.testCaseCode)) &&
                (filters.suiteName === '' || suiteName.includes(filters.suiteName)) &&
                (filters.moduleName === '' || moduleName.includes(filters.moduleName)) &&
                (filters.labels === '' || labels.includes(filters.labels))
            );

            if (matches) {
                row.show();
                visibleCount++;
            } else {
                row.hide();
            }
        });

        updateVisibleCount(visibleCount);
    }

    function clearAllFilters() {
        $('#filterTestCaseName, #filterTestCaseCode, #filterSuiteName, #filterModuleName, #filterLabels').val('');
        $('#testCasesTable tbody tr').show();
        updateVisibleCount($('#testCasesTable tbody tr').length);
    }

    function selectAllTestCases() {
        $('.test-case-checkbox').each(function () {
            const testCaseCode = $(this).val();
            selectedTestCases.add(testCaseCode);
            $(this).prop('checked', true);
        });
        updateSelectedCount();
        updateSelectAllCheckbox();
    }

    function selectNoTestCases() {
        selectedTestCases.clear();
        $('.test-case-checkbox').prop('checked', false);
        updateSelectedCount();
        updateSelectAllCheckbox();
    }

    function selectVisibleTestCases() {
        $('#testCasesTable tbody tr:visible .test-case-checkbox').each(function () {
            const testCaseCode = $(this).val();
            selectedTestCases.add(testCaseCode);
            $(this).prop('checked', true);
        });
        updateSelectedCount();
        updateSelectAllCheckbox();
    }

    function toggleAllCheckboxes() {
        const isChecked = $('#selectAllCheckbox').is(':checked');
        if (isChecked) {
            selectAllTestCases();
        } else {
            selectNoTestCases();
        }
    }

    function handleCheckboxChange() {
        const testCaseCode = $(this).val();
        const isChecked = $(this).is(':checked');

        if (isChecked) {
            selectedTestCases.add(testCaseCode);
        } else {
            selectedTestCases.delete(testCaseCode);
        }

        updateSelectedCount();
        updateSelectAllCheckbox();
    }

    function updateSelectedCount() {
        const count = selectedTestCases.size;
        $('#selectedCount').text(count);
        $('#runSelectedTests').prop('disabled', count === 0);
    }

    function updateVisibleCount(count) {
        // Update total count display if needed
    }

    function updateSelectAllCheckbox() {
        const totalCheckboxes = $('.test-case-checkbox').length;
        const checkedCheckboxes = $('.test-case-checkbox:checked').length;

        if (checkedCheckboxes === 0) {
            $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAllCheckbox').prop('indeterminate', true);
        }
    }

    function showRunTestsModal() {
        if (selectedTestCases.size === 0) {
            alert('Please select at least one test case to run.');
            return;
        }

        $('#modalSelectedCount').text(selectedTestCases.size);

        // Populate selected tests list
        const selectedTestsUl = $('#selectedTestsUl');
        selectedTestsUl.empty();

        selectedTestCases.forEach(testCaseCode => {
            const row = $(`tr[data-testcase-code="${testCaseCode}"]`);
            const testCaseName = row.find('.test-case-name').text();
            const listItem = $(`<li class="list-group-item d-flex justify-content-between align-items-center">
                <span><strong>${testCaseCode}</strong> - ${testCaseName}</span>
            </li>`);
            selectedTestsUl.append(listItem);
        });

        $('#runTestsModal').modal('show');
    }

    function runSelectedTests() {
        const notes = $('#runNotes').val();
        const selectedTestCasesArray = Array.from(selectedTestCases);

        // Show loading state
        $('#confirmRunTests').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Starting...');

        // First, save the selection
        $.ajax({
            url: '/TestCase/SaveSelection',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                selectedTestCases: selectedTestCasesArray,
                notes: notes
            }),
            success: function (response) {
                if (response.success) {
                    // Now trigger the test run
                    $.ajax({
                        url: '/TestCase/RunTests',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            runId: response.runId
                        }),
                        success: function (runResponse) {
                            $('#runTestsModal').modal('hide');
                            selectNoTestCases();
                            if (runResponse.success) {
                                showSuccessMessage('Tests started successfully!', runResponse.buildUrl);
                            } else {
                                showErrorMessage('Failed to start tests: ' + runResponse.message);
                            }
                        },
                        error: function () {
                            showErrorMessage('Failed to start test run. Please try again.');
                        },
                        complete: function () {
                            $('#confirmRunTests').prop('disabled', false).html('<i class="fas fa-play"></i> Start Test Run');
                        }
                    });
                } else {
                    showErrorMessage('Failed to save test selection. Please try again.');
                    $('#confirmRunTests').prop('disabled', false).html('<i class="fas fa-play"></i> Start Test Run');
                }
            },
            error: function () {
                showErrorMessage('Failed to save test selection. Please try again.');
                $('#confirmRunTests').prop('disabled', false).html('<i class="fas fa-play"></i> Start Test Run');
            }
        });
    }

    function showTestRunsModal() {
        $('#testRunsModal').modal('show');
        loadTestRuns();
    }

    function loadTestRuns() {
        $('#testRunsContent').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');

        $.ajax({
            url: '/TestCase/GetTestRuns',
            method: 'GET',
            success: function (testRuns) {
                displayTestRuns(testRuns);
            },
            error: function () {
                $('#testRunsContent').html('<div class="alert alert-danger">Failed to load test runs.</div>');
            }
        });
    }

    function displayTestRuns(testRuns) {
        if (testRuns.length === 0) {
            $('#testRunsContent').html('<div class="alert alert-info">No test runs found.</div>');
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
        html += '<th>Run ID</th><th>Created</th><th>Status</th><th>Test Cases</th><th>Actions</th></tr></thead><tbody>';

        testRuns.forEach(run => {
            const statusBadge = getStatusBadge(run.status);
            const createdDate = new Date(run.createdAt).toLocaleString();

            html += `<tr>
                <td><small>${run.runId}</small></td>
                <td>${createdDate}</td>
                <td>${statusBadge}</td>
                <td>${run.testCaseCount}</td>
                <td>`;

            if (run.azureDevOpsBuildUrl) {
                html += `<a href="${run.azureDevOpsBuildUrl}" target="_blank" class="btn btn-sm btn-outline-primary">View Build</a>`;
            }

            html += `</td></tr>`;
        });

        html += '</tbody></table></div>';
        $('#testRunsContent').html(html);
    }

    function getStatusBadge(status) {
        const statusMap = {
            0: '<span class="badge bg-secondary">Created</span>',
            1: '<span class="badge bg-warning">Queued</span>',
            2: '<span class="badge bg-primary">Running</span>',
            3: '<span class="badge bg-success">Completed</span>',
            4: '<span class="badge bg-danger">Failed</span>',
            5: '<span class="badge bg-secondary">Cancelled</span>'
        };
        return statusMap[status] || '<span class="badge bg-secondary">Unknown</span>';
    }

    function showSuccessMessage(message, buildUrl) {
        let alertHtml = `<div class="alert alert-success alert-dismissible fade show" role="alert">
            ${message}`;

        if (buildUrl) {
            alertHtml += ` <a href="${buildUrl}" target="_blank" class="alert-link">View Build Progress</a>`;
        }

        alertHtml += `<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>`;

        $('#messageContainer').prepend(alertHtml);

        // Auto-dismiss after 10 seconds
        setTimeout(() => {
            $('.alert-success').alert('close');
        }, 10000);
    }

    function showErrorMessage(message) {
        const alertHtml = `<div class="alert alert-danger alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;

        $('#messageContainer').prepend(alertHtml);

        // Auto-dismiss after 8 seconds
        setTimeout(() => {
            $('.alert-danger').alert('close');
        }, 8000);
    }
});