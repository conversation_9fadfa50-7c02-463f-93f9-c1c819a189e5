using System.Text;
using System.Text.Json;

namespace TestFramWorkWeb.Services
{
    public class AzureDevOpsService : IAzureDevOpsService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AzureDevOpsService> _logger;

        public AzureDevOpsService(HttpClient httpClient, IConfiguration configuration, ILogger<AzureDevOpsService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<BuildResult> TriggerBuildAsync(Guid runId)
        {
            try
            {
                var baseUrl = "https://devops1.ebseg.com/DevWork/Test%20Automation%20RandD/_apis/build/builds";
                var apiVersion = "7.0";
                var url = $"{baseUrl}?api-version={apiVersion}";

                // Get configuration values
                var personalAccessToken = _configuration["AzureDevOps:PersonalAccessToken"];
                var buildDefinitionId = _configuration["AzureDevOps:BuildDefinitionId"];

                if (string.IsNullOrEmpty(personalAccessToken))
                {
                    return new BuildResult
                    {
                        Success = false,
                        ErrorMessage = "Azure DevOps Personal Access Token not configured"
                    };
                }

                if (string.IsNullOrEmpty(buildDefinitionId))
                {
                    return new BuildResult
                    {
                        Success = false,
                        ErrorMessage = "Azure DevOps Build Definition ID not configured"
                    };
                }

                // Prepare the build request
                var buildRequest = new
                {
                    definition = new { id = int.Parse(buildDefinitionId) },
                    parameters = JsonSerializer.Serialize(new { RunId = runId.ToString() }),
                    reason = "manual",
                    requestedFor = new { displayName = "Test Framework Web" }
                };


                var jsonContent = JsonSerializer.Serialize(buildRequest);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Set up authentication
                var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{personalAccessToken}"));
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authToken);

                _logger.LogInformation($"Triggering Azure DevOps build for RunId: {runId}");

                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var buildResponse = JsonSerializer.Deserialize<AzureDevOpsBuildResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    _logger.LogInformation($"Build triggered successfully. Build ID: {buildResponse.Id}");

                    return new BuildResult
                    {
                        Success = true,
                        BuildId = buildResponse.Id.ToString(),
                        BuildUrl = buildResponse._links?.Web?.Href
                    };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to trigger build. Status: {response.StatusCode}, Content: {errorContent}");

                    return new BuildResult
                    {
                        Success = false,
                        ErrorMessage = $"HTTP {response.StatusCode}: {errorContent}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Exception occurred while triggering build for RunId: {runId}");
                return new BuildResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
    }

    // Response models for Azure DevOps API
    public class AzureDevOpsBuildResponse
    {
        public int Id { get; set; }
        public string BuildNumber { get; set; }
        public string Status { get; set; }
        public BuildLinks _links { get; set; }
    }

    public class BuildLinks
    {
        public BuildLink Web { get; set; }
    }

    public class BuildLink
    {
        public string Href { get; set; }
    }
}
