@model IEnumerable<TestFramWorkWeb.Controllers.TestCaseDisplayModel>
@{
    ViewData["Title"] = "Test Case Management";
}

<div class="container-fluid" id="messageContainer">
    <div class="row">
        <div class="col-12">
            <h2>Test Case Management</h2>
            <p class="text-muted">Select test cases to run and manage test executions</p>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="filterTestCaseName" class="form-label">Test Case Name</label>
                            <input type="text" class="form-control" id="filterTestCaseName" placeholder="Filter by name...">
                        </div>
                        <div class="col-md-2">
                            <label for="filterTestCaseCode" class="form-label">Test Case Code</label>
                            <input type="text" class="form-control" id="filterTestCaseCode" placeholder="Filter by code...">
                        </div>
                        <div class="col-md-2">
                            <label for="filterSuiteName" class="form-label">Suite Name</label>
                            <input type="text" class="form-control" id="filterSuiteName" placeholder="Filter by suite...">
                        </div>
                        <div class="col-md-2">
                            <label for="filterModuleName" class="form-label">Module Name</label>
                            <input type="text" class="form-control" id="filterModuleName" placeholder="Filter by module...">
                        </div>
                        <div class="col-md-2">
                            <label for="filterLabels" class="form-label">Labels</label>
                            <input type="text" class="form-control" id="filterLabels" placeholder="Filter by labels...">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-secondary d-block" id="clearFilters">Clear</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Selection Controls -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="selectAll">Select All</button>
                <button type="button" class="btn btn-outline-primary" id="selectNone">Select None</button>
                <button type="button" class="btn btn-outline-primary" id="selectVisible">Select Visible</button>
            </div>
            <span class="ms-3 text-muted">
                Selected: <span id="selectedCount">0</span> / <span id="totalCount">@Model.Count()</span>
            </span>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-success" id="runSelectedTests" disabled>
                <i class="fas fa-play"></i> Run Selected Tests
            </button>
            <button type="button" class="btn btn-info" id="viewTestRuns">
                <i class="fas fa-history"></i> View Test Runs
            </button>
        </div>
    </div>

    <!-- Test Cases Table -->
    <div class="row">
        <div class="col-12">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="testCasesTable">
                    <thead class="table-dark">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                            </th>
                            <th>Name</th>
                            <th>Code</th>
                            <th>Suite Name</th>
                            <th>Module Name</th>
                            <th>Labels</th>
                            <th>Owners</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var testCase in Model)
                        {
                            <tr data-testcase-code="@testCase.TestCaseCode">
                                <td>
                                    <input type="checkbox" class="form-check-input test-case-checkbox" 
                                           value="@testCase.TestCaseCode">
                                </td>
                                <td class="test-case-name">@testCase.TestCaseName</td>
                                <td class="test-case-code">@testCase.TestCaseCode</td>
                                <td class="suite-name">@testCase.SuiteName</td>
                                <td class="module-name">@testCase.ModuleName</td>
                                <td class="labels">
                                    @if (testCase.Labels != null && testCase.Labels.Any())
                                    {
                                        @foreach (var label in testCase.Labels)
                                        {
                                            <span class="badge bg-secondary me-1">@label</span>
                                        }
                                    }
                                </td>
                                <td class="owners">
                                    @if (testCase.Owners != null && testCase.Owners.Any())
                                    {
                                        @string.Join(", ", testCase.Owners)
                                    }
                                </td>
                                <td class="description">
                                    @if (!string.IsNullOrEmpty(testCase.Description))
                                    {
                                        <span title="@testCase.Description">
                                            @(testCase.Description.Length > 50 ? testCase.Description.Substring(0, 50) + "..." : testCase.Description)
                                        </span>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Run Tests Modal -->
<div class="modal fade" id="runTestsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Run Selected Tests</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>You have selected <span id="modalSelectedCount">0</span> test case(s) to run.</p>
                <div class="mb-3">
                    <label for="runNotes" class="form-label">Notes (Optional)</label>
                    <textarea class="form-control" id="runNotes" rows="3" placeholder="Add any notes about this test run..."></textarea>
                </div>
                <div id="selectedTestsList" class="mb-3">
                    <h6>Selected Test Cases:</h6>
                    <ul id="selectedTestsUl" class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmRunTests">
                    <i class="fas fa-play"></i> Start Test Run
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Test Runs History Modal -->
<div class="modal fade" id="testRunsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Run History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="testRunsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/testcase-management.js"></script>
}
