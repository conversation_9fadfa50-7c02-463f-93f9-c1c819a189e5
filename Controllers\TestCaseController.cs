using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using TestFramWorkWeb.DatabaseAccess;
using TestFramWorkWeb.Models;
using TestFramWorkWeb.Services;

namespace TestFramWorkWeb.Controllers
{
    public class TestCaseController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IAzureDevOpsService _azureDevOpsService;

        public TestCaseController(ApplicationDbContext context, IAzureDevOpsService azureDevOpsService)
        {
            _context = context;
            _azureDevOpsService = azureDevOpsService;
        }

        public async Task<IActionResult> Index()
        {
            var testCaseWrappers = await _context.TestCases.ToListAsync();
            var testCases = testCaseWrappers.Select(wrapper => 
            {
                var testCase = JsonSerializer.Deserialize<TestCase>(wrapper.TestContent);
                return new TestCaseDisplayModel
                {
                    TestCaseCode = testCase.TestCaseCode,
                    TestCaseName = testCase.TestCaseName,
                    SuiteName = testCase.SuiteName ?? "N/A",
                    ModuleName = testCase.ModuleName ?? "N/A",
                    Labels = testCase.Labels ?? new string[0],
                    Owners = testCase.Owners ?? new string[0],
                    Description = testCase.Description
                };
            }).ToList();

            return View(testCases);
        }

        [HttpPost]
        public async Task<IActionResult> SaveSelection([FromBody] TestRunRequest request)
        {
            if (request?.SelectedTestCases == null || !request.SelectedTestCases.Any())
            {
                return BadRequest("No test cases selected");
            }

            var testRun = new TestRun
            {
                RunId = Guid.NewGuid(),
                TestRunSelections = JsonSerializer.Serialize(request.SelectedTestCases),
                Notes = request.Notes,
                CreatedAt = DateTime.UtcNow,
                Status = TestRunStatus.Created
            };

            _context.TestRuns.Add(testRun);

            await _context.SaveChangesAsync();

            return Json(new { success = true, runId = testRun.RunId });
        }

        [HttpPost]
        public async Task<IActionResult> RunTests([FromBody] RunTestsRequest request)
        {
            if (request?.RunId == null)
            {
                return BadRequest("RunId is required");
            }

            var testRun = await _context.TestRuns
                .FirstOrDefaultAsync(tr => tr.RunId == request.RunId);

            if (testRun == null)
            {
                return NotFound("Test run not found");
            }

            try
            {
                // Update test run status
                testRun.Status = TestRunStatus.Queued;
                testRun.StartedAt = DateTime.UtcNow;

                // Trigger Azure DevOps build
                var buildResult = await _azureDevOpsService.TriggerBuildAsync(request.RunId);
                
                if (buildResult.Success)
                {
                    testRun.AzureDevOpsBuildId = buildResult.BuildId;
                    testRun.AzureDevOpsBuildUrl = buildResult.BuildUrl;
                    testRun.Status = TestRunStatus.Running;
                }
                else
                {
                    testRun.Status = TestRunStatus.Failed;
                    testRun.Notes = $"Failed to trigger build: {buildResult.ErrorMessage}";
                }

                await _context.SaveChangesAsync();

                return Json(new { 
                    success = buildResult.Success, 
                    message = buildResult.Success ? "Tests started successfully" : buildResult.ErrorMessage,
                    buildUrl = buildResult.BuildUrl
                });
            }
            catch (Exception ex)
            {
                testRun.Status = TestRunStatus.Failed;
                testRun.Notes = $"Error: {ex.Message}";
                await _context.SaveChangesAsync();

                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTestRuns()
        {
            var testRuns = await _context.TestRuns
                .OrderByDescending(tr => tr.CreatedAt)
                .Take(50)
                .Select(tr => new
                {
                    tr.RunId,
                    tr.CreatedAt,
                    tr.Status,
                    tr.AzureDevOpsBuildUrl,
                    tr.TestRunSelections,
                    tr.Notes
                })
                .ToListAsync();

            var result = testRuns.Select(tr => new
            {
                tr.RunId,
                tr.CreatedAt,
                tr.Status,
                tr.AzureDevOpsBuildUrl,
                TestCaseCount = JsonSerializer.Deserialize<string[]>(tr.TestRunSelections)?.Length,
                tr.Notes
            }).ToList();


            return Json(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetTestRunDetails(Guid runId)
        {
            var testRun = await _context.TestRuns
                .FirstOrDefaultAsync(tr => tr.RunId == runId);

            if (testRun == null)
            {
                return NotFound();
            }

            var testcases = JsonSerializer.Deserialize<string[]>(testRun.TestRunSelections);
            var result = new
            {
                testRun.RunId,
                testRun.CreatedAt,
                testRun.Status,
                testRun.AzureDevOpsBuildUrl,
                testRun.StartedAt,
                testRun.CompletedAt,
                testRun.Notes,
                TestCases = testcases
            };

            return Json(result);
        }
    }

    public class TestCaseDisplayModel
    {
        public string TestCaseCode { get; set; }
        public string TestCaseName { get; set; }
        public string SuiteName { get; set; }
        public string ModuleName { get; set; }
        public string[] Labels { get; set; }
        public string[] Owners { get; set; }
        public string Description { get; set; }
    }

    public class TestRunRequest
    {
        public string[] SelectedTestCases { get; set; }
        public string Notes { get; set; }
    }

    public class RunTestsRequest
    {
        public Guid RunId { get; set; }
    }
}
