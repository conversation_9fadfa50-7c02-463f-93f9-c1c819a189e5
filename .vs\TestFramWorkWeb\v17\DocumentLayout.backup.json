{"Version": 1, "WorkspaceRootPath": "D:\\PROJECTS\\TestFramWorkWeb\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\models\\testcase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:models\\testcase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\models\\module.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:models\\module.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\wwwroot\\js\\testcase-management.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:wwwroot\\js\\testcase-management.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\services\\azuredevopsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:services\\azuredevopsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\views\\testcase\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:views\\testcase\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 16, "Children": [{"$type": "Bookmark", "Name": "ST:12:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{bdada759-5db7-402f-96e3-402b17c8c5b4}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Module.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\Module.cs", "RelativeDocumentMoniker": "Models\\Module.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\Module.cs", "RelativeToolTip": "Models\\Module.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-08T07:49:37.042Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAACEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-08T07:30:24.85Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "testcase-management.js", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\js\\testcase-management.js", "RelativeDocumentMoniker": "wwwroot\\js\\testcase-management.js", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\js\\testcase-management.js", "RelativeToolTip": "wwwroot\\js\\testcase-management.js", "ViewState": "AgIAAMYAAAAAAAAAAAAiwNQAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-09-07T14:51:49.674Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Index.cshtml", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Views\\TestCase\\Index.cshtml", "RelativeDocumentMoniker": "Views\\TestCase\\Index.cshtml", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Views\\TestCase\\Index.cshtml", "RelativeToolTip": "Views\\TestCase\\Index.cshtml", "ViewState": "AgIAAE4AAAAAAAAAAAAAAFYAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-07T14:37:53.319Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AzureDevOpsService.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Services\\AzureDevOpsService.cs", "RelativeDocumentMoniker": "Services\\AzureDevOpsService.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Services\\AzureDevOpsService.cs", "RelativeToolTip": "Services\\AzureDevOpsService.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAAADgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T13:44:46.157Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "TestCase.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\TestCase.cs", "RelativeDocumentMoniker": "Models\\TestCase.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\TestCase.cs*", "RelativeToolTip": "Models\\TestCase.cs*", "ViewState": "AgIAAE8AAAAAAAAAAAAkwFoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T13:35:52.004Z", "EditorCaption": ""}]}, {"DockedWidth": 1304, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}