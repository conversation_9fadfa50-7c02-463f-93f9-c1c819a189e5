﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TestFramWorkWeb.DatabaseAccess;

#nullable disable

namespace TestFramWorkWeb.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250907121952_initial")]
    partial class initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.19")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("TestFramWorkWeb.Models.TestCaseWrapper", b =>
                {
                    b.Property<string>("TestCaseCode")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("LastChangeTime")
                        .HasColumnType("datetime2(7)");

                    b.Property<string>("TestContent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("TestCaseCode");

                    b.ToTable("TestCases");
                });

            modelBuilder.Entity("TestFramWorkWeb.Models.TestRun", b =>
                {
                    b.Property<Guid>("RunId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AzureDevOpsBuildId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AzureDevOpsBuildUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TestRunSelections")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("RunId");

                    b.ToTable("TestRuns");
                });
#pragma warning restore 612, 618
        }
    }
}
