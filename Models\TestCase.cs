﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics;

namespace TestFramWorkWeb.Models
{
    public class TestCaseWrapper
    {
        [Key]
        public string TestCaseCode { get; set; }
        public string TestContent { get; set; }
        [Column(TypeName = "datetime2(7)")]
        public DateTime LastChangeTime { get; set; }
    }

    public class TestCase
    {
        public string TestCaseName { get; set; }
        public string TestCaseCode { get; set; }
        public string Description { get; set; }
        public string[] Browser { get; set; } // Added property
        public string BaseUrl { get; set; } // Added property
        public List<TestStep> Steps { get; set; }
        public string SuiteName { get; set; }
        public string ModuleName { get; set; }
        public Guid TestCaseId { get; set; } = new Guid();
        //public Parameters Params { get; internal set; }
        public Stopwatch ExecutionTimeFromFirstStepInMS { get; set; }
        public string[] Labels { get; set; }
        public string[] Owners { get; set; }

        public string Environment { get; set; }
        public TestData TestData { get; set; } = new TestData();
    }

    public class TestStep
    {
        public string Name { get; set; }
        public string Command { get; set; } // "Input", "Click", "Verify", etc.
        public string Target { get; set; }
        public string Value { get; set; }
        public string CustomStatusToReportUponFailure { get; set; }
        public bool ContinueOnError { get; set; }
        public TestCaseReferenceClass TestCaseReference { get; set; } //TestCases/CommonSteps/LoginSteps.json
        public bool SkipWaitingSpinner { get; set; }
        public int CustomWaitTimeForSpinnerInMilliseconds { get; set; }
        public int CustomDelayBeforeStepExecustionInMilliseconds { get; set; }
        public string ElementToValidateThatScreenLoaded { get; set; }
        public string NavigateToUrl { get; set; }
        public List<CaptureTarget> TargetsToCaptureDataFrom { get; set; }
        // NEW: Environment-specific overrides
        public Dictionary<string, TestStep> SpecialEnvTestStepParams { get; set; }
        public string TestDatasReference { get; set; }

        //public TestSuiteItem TestCaseReference { get; set; }

    }
    public class TestData
    {
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        // if i want specific ExcludedTestData per test later i will handle it 
        public List<string> ExcludedTestData = new List<string>();
    }

    public class CaptureTarget
    {
        public string UniqueKey { get; set; }
        public string Selector { get; set; }
        public string Regex { get; set; }
    }

    public class TestCaseInfo
    {
        public TestCase TestCase { get; set; }
        public DateTime LastModified { get; set; }
    }

    public class TestRun
    {
        [Key]
        public Guid RunId { get; set; } = Guid.NewGuid();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public TestRunStatus Status { get; set; } = TestRunStatus.Created;
        public string? AzureDevOpsBuildId { get; set; }
        public string? AzureDevOpsBuildUrl { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? Notes { get; set; }

        public byte[] ReportFile { get; set; }

        public string TestRunSelections { get; set; }
    }

    public enum TestRunStatus
    {
        Created = 0,
        Queued = 1,
        Running = 2,
        Completed = 3,
        Failed = 4,
        Cancelled = 5
    }
}
