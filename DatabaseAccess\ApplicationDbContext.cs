﻿using TestFramWorkWeb.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestFramWorkWeb.DatabaseAccess
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }
        public DbSet<TestCaseWrapper> TestCases { get; set; }
        public DbSet<TestRun> TestRuns { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure TestRun entity
            modelBuilder.Entity<TestRun>(entity =>
            {
                entity.HasKey(e => e.RunId);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

           
        }
    }
}
