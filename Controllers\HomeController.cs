using System.Diagnostics;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using TestFramWorkWeb.DatabaseAccess;
using TestFramWorkWeb.Models;

namespace TestFramWorkWeb.Controllers
{
    public class HomeController : Controller
    {

        public HomeController()
        {
        }

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
